/**
 * Figma API 封装
 * 提供获取Figma文件节点信息的功能
 */

import { get } from '../lib/request';
import { GLOBAL_CONFIG } from '../../config';

// Figma API 响应类型定义
export interface FigmaNode {
  id: string;
  name: string;
  type: string;
  visible?: boolean;
  locked?: boolean;
  exportSettings?: any[];
  blendMode?: string;
  preserveRatio?: boolean;
  constraints?: any;
  layoutAlign?: string;
  layoutGrow?: number;
  layoutSizingHorizontal?: string;
  layoutSizingVertical?: string;
  transitionNodeID?: string;
  transitionDuration?: number;
  transitionEasing?: string;
  opacity?: number;
  absoluteBoundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  absoluteRenderBounds?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  effects?: any[];
  size?: {
    x: number;
    y: number;
  };
  relativeTransform?: number[][];
  isMask?: boolean;
  fills?: any[];
  fillGeometry?: any[];
  strokes?: any[];
  strokeWeight?: number;
  strokeAlign?: string;
  strokeGeometry?: any[];
  cornerRadius?: number;
  cornerSmoothing?: number;
  children?: FigmaNode[];
  characters?: string;
  style?: any;
  layoutVersion?: number;
  characterStyleOverrides?: any[];
  styleOverrideTable?: any;
  lineTypes?: string[];
  lineIndentations?: number[];
}

export interface FigmaNodesResponse {
  name: string;
  role: string;
  lastModified: string;
  editorType: string;
  thumbnailUrl: string;
  version: string;
  nodes: {
    [nodeId: string]: {
      document: FigmaNode;
      components: any;
      componentSets: any;
      schemaVersion: number;
      styles: any;
    };
  };
  err?: string;
}

// Figma 链接解析结果
export interface FigmaLinkInfo {
  fileKey: string;
  nodeId?: string;
  originalUrl: string;
}

/**
 * 解析 Figma 分享链接，提取文件 key 和节点 ID
 * 支持的链接格式：
 * - https://www.figma.com/file/[file_key]/[file_name]
 * - https://www.figma.com/file/[file_key]/[file_name]?node-id=[node_id]
 * - https://www.figma.com/design/[file_key]/[file_name]
 * - https://www.figma.com/design/[file_key]/[file_name]?node-id=[node_id]
 */
export function parseFigmaUrl(url: string): FigmaLinkInfo {
  try {
    const parsedUrl = new URL(url);
    
    // 检查是否是 Figma 域名
    if (!parsedUrl.hostname.includes('figma.com')) {
      throw new Error('不是有效的 Figma 链接');
    }

    // 提取路径部分
    const pathParts = parsedUrl.pathname.split('/').filter(part => part.length > 0);
    
    // 检查路径格式 (file 或 design)
    if (pathParts.length < 2 || !['file', 'design'].includes(pathParts[0])) {
      throw new Error('无效的 Figma 链接格式');
    }

    const fileKey = pathParts[1];
    
    // 提取节点 ID (如果存在)
    let nodeId: string | undefined;
    const nodeIdParam = parsedUrl.searchParams.get('node-id');
    if (nodeIdParam) {
      // 将 URL 中的 node-id 格式 (如 46%3A6) 转换为 API 格式 (如 46:6)
      nodeId = decodeURIComponent(nodeIdParam).replace(':', '-');
    }

    return {
      fileKey,
      nodeId,
      originalUrl: url
    };
  } catch (error) {
    throw new Error(`解析 Figma 链接失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 获取 Figma 文件中指定节点的信息
 * @param fileKey Figma 文件的 key
 * @param nodeIds 节点 ID 数组，格式如 ['46-6', '47-8']
 * @returns Promise<FigmaNodesResponse>
 */
export async function getFigmaNodes(fileKey: string, nodeIds: string[]): Promise<FigmaNodesResponse> {
  const baseUrl = 'https://api.figma.com/v1/files';
  const url = `${baseUrl}/${fileKey}/nodes`;
  
  try {
    const response = await get<FigmaNodesResponse>(url, {
      ids: nodeIds.join(',')
    }, {
      headers: {
        'X-FIGMA-TOKEN': GLOBAL_CONFIG.figma_token
      }
    });

    if (response.data.err) {
      throw new Error(`Figma API 错误: ${response.data.err}`);
    }

    return response.data;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`获取 Figma 节点信息失败: ${error.message}`);
    }
    throw new Error('获取 Figma 节点信息失败: 未知错误');
  }
}

/**
 * 根据 Figma 分享链接获取文件信息
 * @param figmaUrl Figma 分享链接
 * @returns Promise<FigmaNodesResponse>
 */
export async function getFigmaInfoFromUrl(figmaUrl: string): Promise<FigmaNodesResponse> {
  // 解析链接
  const linkInfo = parseFigmaUrl(figmaUrl);
  
  // 如果有节点 ID，获取指定节点信息
  if (linkInfo.nodeId) {
    return await getFigmaNodes(linkInfo.fileKey, [linkInfo.nodeId]);
  }
  
  // 如果没有节点 ID，获取文件根节点信息
  // 这里我们可以先获取文件信息，然后获取根节点
  try {
    // 获取文件基本信息
    const fileInfoUrl = `https://api.figma.com/v1/files/${linkInfo.fileKey}`;
    const fileResponse = await get(fileInfoUrl, undefined, {
      headers: {
        'X-FIGMA-TOKEN': GLOBAL_CONFIG.figma_token
      }
    });
    
    // 返回文件基本信息（这里可以根据需要调整返回的数据结构）
    return {
      name: fileResponse.data.name || 'Unknown File',
      role: 'viewer',
      lastModified: fileResponse.data.lastModified || '',
      editorType: 'figma',
      thumbnailUrl: fileResponse.data.thumbnailUrl || '',
      version: fileResponse.data.version || '0',
      nodes: {}
    };
  } catch (error) {
    throw new Error(`获取 Figma 文件信息失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 打印 Figma 文件信息到控制台
 * @param figmaUrl Figma 分享链接
 */
export async function printFigmaInfo(figmaUrl: string): Promise<void> {
  try {
    console.log('🔍 正在解析 Figma 链接...');
    const linkInfo = parseFigmaUrl(figmaUrl);
    
    console.log('📋 链接解析结果:');
    console.log(`  - 文件 Key: ${linkInfo.fileKey}`);
    console.log(`  - 节点 ID: ${linkInfo.nodeId || '无'}`);
    console.log(`  - 原始链接: ${linkInfo.originalUrl}`);
    console.log('');
    
    console.log('📡 正在获取 Figma 文件信息...');
    const figmaInfo = await getFigmaInfoFromUrl(figmaUrl);
    
    console.log('✅ 获取成功！文件信息:');
    console.log(`  - 文件名称: ${figmaInfo.name}`);
    console.log(`  - 最后修改: ${figmaInfo.lastModified}`);
    console.log(`  - 版本: ${figmaInfo.version}`);
    console.log(`  - 缩略图: ${figmaInfo.thumbnailUrl}`);
    
    if (Object.keys(figmaInfo.nodes).length > 0) {
      console.log('  - 节点信息:');
      Object.entries(figmaInfo.nodes).forEach(([nodeId, nodeData]) => {
        console.log(`    * 节点 ${nodeId}:`);
        console.log(`      - 名称: ${nodeData.document.name}`);
        console.log(`      - 类型: ${nodeData.document.type}`);
        if (nodeData.document.absoluteBoundingBox) {
          const box = nodeData.document.absoluteBoundingBox;
          console.log(`      - 尺寸: ${box.width} x ${box.height}`);
          console.log(`      - 位置: (${box.x}, ${box.y})`);
        }
      });
    }
    
    console.log('');
    console.log('🎉 Figma 文件信息获取完成！');
    
  } catch (error) {
    console.error('❌ 获取 Figma 文件信息失败:');
    console.error(`   ${error instanceof Error ? error.message : '未知错误'}`);
    throw error;
  }
}

// 导出所有功能
export default {
  parseFigmaUrl,
  getFigmaNodes,
  getFigmaInfoFromUrl,
  printFigmaInfo
};
